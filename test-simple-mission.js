// Simple test for mission detection
// Run with: node test-simple-mission.js

// Mock the processChatCommand function logic
function testMissionDetection(command) {
  console.log(`Testing: "${command}"`);
  
  // Simple fallback for common mission phrases
  const cmd = command.toLowerCase();
  if (cmd.includes('get all batteries') || 
      cmd.includes('collect all batteries') || 
      cmd.includes('collect all the batteries') ||
      cmd.includes('get every battery')) {
    console.log('✅ Direct mission detection: collect_batteries');
    return {
      actions: [],
      response: "Roger that. Starting mission: collect all batteries.",
      mission: 'collect_batteries',
      toolLog: 'Direct mission detection',
      reasoning: 'Command matched battery collection pattern'
    };
  }
  
  if (cmd.includes('kill all turtles') || 
      cmd.includes('eliminate all turtles') || 
      cmd.includes('destroy all turtles') ||
      cmd.includes('clear all turtles')) {
    console.log('✅ Direct mission detection: eliminate_turtles');
    return {
      actions: [],
      response: "Roger that. Starting mission: eliminate all turtles.",
      mission: 'eliminate_turtles',
      toolLog: 'Direct mission detection',
      reasoning: 'Command matched turtle elimination pattern'
    };
  }
  
  console.log('❌ No mission detected - would fall through to LLM');
  return null;
}

console.log('🧪 Testing Mission Detection Fallback\n');

const testCases = [
  "get all batteries",
  "collect all batteries", 
  "collect all the batteries",
  "get every battery",
  "kill all turtles",
  "eliminate all turtles",
  "destroy all turtles",
  "clear all turtles",
  "move up",
  "fire left",
  "get battery", // Should not match
  "kill turtle"  // Should not match
];

testCases.forEach(command => {
  const result = testMissionDetection(command);
  if (result) {
    console.log(`  Mission: ${result.mission}`);
    console.log(`  Response: ${result.response}`);
  }
  console.log();
});

console.log('📋 Summary:');
console.log('✅ The fallback logic should now catch common mission phrases');
console.log('✅ "get all batteries" should trigger collect_batteries mission');
console.log('✅ This bypasses any LLM API issues');
console.log('✅ The game should now respond to your command!');
