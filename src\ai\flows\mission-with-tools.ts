'use server';

/**
 * @fileOverview Tool-based mission planning using OpenAI function calling
 */

import { openai } from '@/ai/openai-client';
import { z } from 'zod';

const LocationSchema = z.object({
  row: z.number(),
  col: z.number(),
});

export type MissionInput = {
  grid: string[][];
  botRow: number;
  botCol: number;
  objective: string;
  turtleLocations: Array<{row: number, col: number}>;
  batteryLocations: Array<{row: number, col: number}>;
};

export type MissionOutput = {
  action: 'move' | 'fire';
  direction: 'up' | 'down' | 'left' | 'right';
  reason: string;
  toolCalls: string[];
};

// Tool functions that the LLM can call
const tools = [
  {
    type: "function" as const,
    function: {
      name: "calculate_distances",
      description: "Calculate Manhattan distances from bot to all targets",
      parameters: {
        type: "object",
        properties: {
          botRow: { type: "number" },
          botCol: { type: "number" },
          targets: {
            type: "array",
            items: {
              type: "object",
              properties: {
                row: { type: "number" },
                col: { type: "number" }
              }
            }
          }
        },
        required: ["botRow", "botCol", "targets"]
      }
    }
  },
  {
    type: "function" as const,
    function: {
      name: "find_best_target",
      description: "Find the optimal target considering distance, safety, and path efficiency",
      parameters: {
        type: "object",
        properties: {
          botRow: { type: "number" },
          botCol: { type: "number" },
          targets: {
            type: "array",
            items: {
              type: "object",
              properties: {
                row: { type: "number" },
                col: { type: "number" }
              }
            }
          },
          obstacles: {
            type: "array",
            items: {
              type: "object",
              properties: {
                row: { type: "number" },
                col: { type: "number" }
              }
            }
          }
        },
        required: ["botRow", "botCol", "targets"]
      }
    }
  },
  {
    type: "function" as const,
    function: {
      name: "get_next_move",
      description: "Calculate the next move direction toward a target",
      parameters: {
        type: "object",
        properties: {
          botRow: { type: "number" },
          botCol: { type: "number" },
          targetRow: { type: "number" },
          targetCol: { type: "number" }
        },
        required: ["botRow", "botCol", "targetRow", "targetCol"]
      }
    }
  },
  {
    type: "function" as const,
    function: {
      name: "check_line_of_sight",
      description: "Check if bot can fire at target (same row or column)",
      parameters: {
        type: "object",
        properties: {
          botRow: { type: "number" },
          botCol: { type: "number" },
          targetRow: { type: "number" },
          targetCol: { type: "number" }
        },
        required: ["botRow", "botCol", "targetRow", "targetCol"]
      }
    }
  }
];

// Tool implementations
function calculateDistances(botRow: number, botCol: number, targets: Array<{row: number, col: number}>) {
  return targets.map(target => ({
    target,
    distance: Math.abs(target.row - botRow) + Math.abs(target.col - botCol)
  })).sort((a, b) => a.distance - b.distance);
}

function findBestTarget(botRow: number, botCol: number, targets: Array<{row: number, col: number}>, obstacles: Array<{row: number, col: number}> = []) {
  const distances = calculateDistances(botRow, botCol, targets);
  
  // Score targets based on multiple factors
  const scored = distances.map(item => {
    let score = 0;
    
    // Prefer closer targets
    score += (20 - item.distance);
    
    // Prefer same row or column (easier path)
    if (item.target.row === botRow) score += 10; // Same row
    if (item.target.col === botCol) score += 15; // Same column (even better)
    
    // Penalize targets near obstacles
    const nearObstacles = obstacles.filter(obs => 
      Math.abs(obs.row - item.target.row) <= 1 && 
      Math.abs(obs.col - item.target.col) <= 1
    ).length;
    score -= nearObstacles * 3;
    
    return { ...item, score };
  });
  
  scored.sort((a, b) => b.score - a.score);
  return scored[0];
}

function getNextMove(botRow: number, botCol: number, targetRow: number, targetCol: number) {
  if (targetRow > botRow) return 'down';
  if (targetRow < botRow) return 'up';
  if (targetCol > botCol) return 'right';
  if (targetCol < botCol) return 'left';
  return 'none'; // Already at target
}

function checkLineOfSight(botRow: number, botCol: number, targetRow: number, targetCol: number) {
  const sameRow = botRow === targetRow;
  const sameCol = botCol === targetCol;
  
  if (sameRow) {
    return {
      canFire: true,
      direction: targetCol > botCol ? 'right' : 'left'
    };
  }
  
  if (sameCol) {
    return {
      canFire: true,
      direction: targetRow > botRow ? 'down' : 'up'
    };
  }
  
  return { canFire: false, direction: null };
}

export async function executeMissionWithTools(input: MissionInput): Promise<MissionOutput> {
  const gridDisplay = input.grid.map(row => row.join('')).join('\n');

  const messages: any[] = [
    {
      role: "system",
      content: `You are an expert AI strategist for the Emoji Bot Arena game. Use the provided tools to analyze the game state and make optimal decisions.

COORDINATE SYSTEM:
- Row 0 is TOP, row numbers INCREASE going DOWN
- Column 0 is LEFT, column numbers INCREASE going RIGHT
- Moving 'up' DECREASES row, 'down' INCREASES row
- Moving 'left' DECREASES column, 'right' INCREASES column

MISSION STRATEGY:
For "${input.objective}":
1. Use calculate_distances to see all target distances
2. Use find_best_target to choose the optimal target
3. For turtle missions: Use check_line_of_sight to see if you can fire
4. Use get_next_move to determine the direction

Always call tools to analyze before making decisions. Be strategic and efficient.`
    },
    {
      role: "user",
      content: `Current game state:
- Bot position: (${input.botRow}, ${input.botCol})
- Mission: ${input.objective}
- Grid:
${gridDisplay}

Batteries: ${input.batteryLocations.map(b => `(${b.row},${b.col})`).join(', ') || 'None'}
Turtles: ${input.turtleLocations.map(t => `(${t.row},${t.col})`).join(', ') || 'None'}

Use the tools to analyze the situation and determine the best next action. Call tools to help you decide, then provide your final decision.`
    }
  ];

  const response = await openai.chat.completions.create({
    model: 'codestral-latest',
    messages,
    tools,
    tool_choice: "auto",
    temperature: 0.1,
  });

  const toolCalls: string[] = [];
  let finalAction = { action: 'move' as const, direction: 'up' as const, reason: 'Default action' };

  // Process tool calls
  if (response.choices[0].message.tool_calls) {
    for (const toolCall of response.choices[0].message.tool_calls) {
      const { name, arguments: args } = toolCall.function;
      const parsedArgs = JSON.parse(args);
      
      toolCalls.push(`${name}(${JSON.stringify(parsedArgs)})`);
      
      let result;
      switch (name) {
        case 'calculate_distances':
          result = calculateDistances(parsedArgs.botRow, parsedArgs.botCol, parsedArgs.targets);
          break;
        case 'find_best_target':
          result = findBestTarget(parsedArgs.botRow, parsedArgs.botCol, parsedArgs.targets, parsedArgs.obstacles);
          break;
        case 'get_next_move':
          result = getNextMove(parsedArgs.botRow, parsedArgs.botCol, parsedArgs.targetRow, parsedArgs.targetCol);
          break;
        case 'check_line_of_sight':
          result = checkLineOfSight(parsedArgs.botRow, parsedArgs.botCol, parsedArgs.targetRow, parsedArgs.targetCol);
          break;
      }
      
      // Add tool result to conversation
      messages.push({
        role: "assistant",
        content: null,
        tool_calls: [toolCall]
      });

      messages.push({
        role: "tool",
        tool_call_id: toolCall.id,
        content: JSON.stringify(result)
      });
    }
    
    // Get final decision after tool calls
    const finalResponse = await openai.chat.completions.create({
      model: 'codestral-latest',
      messages: [
        ...messages,
        {
          role: "user",
          content: "Based on the tool results, what is your final decision? Respond with JSON: {\"action\": \"move|fire\", \"direction\": \"up|down|left|right\", \"reason\": \"explanation\"}"
        }
      ],
      temperature: 0.1,
      response_format: { type: 'json_object' }
    });
    
    try {
      const decision = JSON.parse(finalResponse.choices[0].message.content || '{}');
      finalAction = {
        action: decision.action || 'move',
        direction: decision.direction || 'up',
        reason: decision.reason || 'Tool-based decision'
      };
    } catch (error) {
      console.error('Failed to parse final decision:', error);
    }
  }

  return {
    ...finalAction,
    toolCalls
  };
}
