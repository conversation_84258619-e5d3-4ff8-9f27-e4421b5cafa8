'use server';

/**
 * @fileOverview A Genkit flow that suggests the optimal direction to fire the laser in the Emoji Bot Arena game.
 *
 * - suggestLaserDirection - A function that suggests the best direction to fire the laser.
 * - SuggestLaserDirectionInput - The input type for the suggestLaserDirection function.
 * - SuggestLaserDirectionOutput - The return type for the suggestLaserDirection function.
 */

import {ai, geminiFlash} from '@/ai/genkit';
import {z} from 'zod';
import { SuggestLaserDirectionInputSchema } from '@/ai/schemas';

export type SuggestLaserDirectionInput = z.infer<
  typeof SuggestLaserDirectionInputSchema
>;

const SuggestLaserDirectionOutputSchema = z.object({
  direction: z
    .enum(['up', 'down', 'left', 'right'])
    .describe('The suggested direction to fire the laser.'),
  reason: z.string().describe('The reasoning behind the suggested direction.'),
});
export type SuggestLaserDirectionOutput = z.infer<
  typeof SuggestLaserDirectionOutputSchema
>;

export async function suggestLaserDirection(
  input: SuggestLaserDirectionInput
): Promise<SuggestLaserDirectionOutput> {
  return suggestLaserDirectionFlow(input);
}

const prompt = ai.definePrompt({
  name: 'suggestLaserDirectionPrompt',
  model: geminiFlash,
  input: {schema: SuggestLaserDirectionInputSchema},
  output: {schema: SuggestLaserDirectionOutputSchema},
  prompt: `You are an AI assistant playing the Emoji Bot Arena game. The game is played on a grid of emoji.
Your task is to suggest the optimal direction for the bot to fire its laser to eliminate turtles and improve its score.

Here is the current state of the grid:

{{#each grid}}
{{#each this}}{{{this}}}{{/each}}
{{/each}}

The bot is located at row {{botRow}}, column {{botCol}}.

Consider the location of turtles 🐢 and batteries 🔋 relative to the bot's position. Also consider if a battery is in the way of shooting a turtle. Prioritize directions with multiple turtles or those that clear a path to batteries.

Suggest the best direction (up, down, left, or right) for the bot to fire its laser and explain your reasoning.

{
  "direction": "<direction>",
  "reason": "<reason>"
}

Make sure the direction is one of: up, down, left, or right.
`,
});

const suggestLaserDirectionFlow = ai.defineFlow(
  {
    name: 'suggestLaserDirectionFlow',
    inputSchema: SuggestLaserDirectionInputSchema,
    outputSchema: SuggestLaserDirectionOutputSchema,
  },
  async input => {
    const {output} = await prompt(input);
    return output!;
  }
);
