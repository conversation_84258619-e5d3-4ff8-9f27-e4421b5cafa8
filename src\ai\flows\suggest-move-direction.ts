'use server';

/**
 * @fileOverview A Genkit flow that suggests the optimal direction to move the bot.
 *
 * - suggestMoveDirection - A function that suggests the best direction to move.
 * - SuggestMoveDirectionInput - The input type for the suggestMoveDirection function.
 * - SuggestMoveDirectionOutput - The return type for the suggestMoveDirection function.
 */

import {ai, geminiFlash} from '@/ai/genkit';
import {z} from 'zod';
import { SuggestMoveDirectionInputSchema } from '@/ai/schemas';

export type SuggestMoveDirectionInput = z.infer<
  typeof SuggestMoveDirectionInputSchema
>;

const SuggestMoveDirectionOutputSchema = z.object({
  direction: z
    .enum(['up', 'down', 'left', 'right', 'none'])
    .describe('The suggested direction to move the bot. "none" if no move is possible or needed.'),
  reason: z.string().describe('The reasoning behind the suggested direction.'),
});
export type SuggestMoveDirectionOutput = z.infer<
  typeof SuggestMoveDirectionOutputSchema
>;

export async function suggestMoveDirection(
  input: SuggestMoveDirectionInput
): Promise<SuggestMoveDirectionOutput> {
  return suggestMoveDirectionFlow(input);
}

const prompt = ai.definePrompt({
  name: 'suggestMoveDirectionPrompt',
  model: geminiFlash,
  input: {schema: SuggestMoveDirectionInputSchema},
  output: {schema: SuggestMoveDirectionOutputSchema},
  prompt: `You are a meticulous and logical AI assistant playing the Emoji Bot Arena game. Your task is to suggest the single most optimal move for the bot to achieve a given objective. You must be precise and avoid getting stuck in loops.

**1. Analyze the Game State:**
- The bot 🤖 is at row {{botRow}}, column {{botCol}}.
- The user's high-level objective is: "{{objective}}"
- The grid is:
{{#each grid}}
{{#each this}}{{{this}}}{{/each}}
{{/each}}
{{#if batteryLocations}}
- Batteries 🔋 locations:
{{#each batteryLocations}}
  - row {{this.row}}, col {{this.col}}
{{/each}}
{{/if}}
{{#if turtleLocations}}
- Turtles 🐢 locations:
{{#each turtleLocations}}
  - row {{this.row}}, col {{this.col}}
{{/each}}
{{/if}}

**2. Determine the Best Move based on Objective:**

**If the objective contains "batteries":**
a. Find the battery with the minimum Manhattan distance (|x1 - x2| + |y1 - y2|) from the bot. This is your target for this turn. Tie-break using lower row, then lower column.
b. **Choose Direction Logic**:
   - If the target is on the same row as the bot, you MUST move horizontally ('left' or 'right') towards it.
   - If the target is on the same column as the bot, you MUST move vertically ('up' or 'down') towards it.
   - If the target is diagonal to the bot, you MUST prioritize moving vertically ('up' or 'down') towards it. The horizontal move is a backup.
c. **Safety Check**:
   - Check if your chosen move is safe (doesn't land on a '🐢').
   - If it is not safe, and you had a backup horizontal move (from the diagonal case), check if that backup is safe. If so, use it.
   - If all options that reduce distance are unsafe, try to move sideways to go around the obstacle.
   - If no safe move is possible, return 'none'.
d. Provide a reason stating the target location and your chosen move.

**If the objective contains "turtle":**
a. Find the turtle with the minimum Manhattan distance from the bot. This is your target for this turn. Use the same tie-breaking rule (lower row, then lower col).
b. Your goal is to get on the same row or column as the target for a clear shot.
c. First, try to align vertically. If the bot is not on the same row as the target, suggest a move (up or down) to match the target's row.
d. If the bot is already on the same row as the target, suggest a move (left or right) to get closer on that row.
e. **CRITICAL SAFETY AND OBSTACLE CHECKS:** Before deciding:
    i. Ensure the move is not into a turtle ('🐢').
    ii. If you are already aligned on a row or column, ensure the path to the turtle is not blocked by a battery ('🔋'). If it is, you must suggest a move to get off that row/column to re-approach from a different angle.
f. Provide a clear reason for your move, mentioning the target turtle and your strategy (e.g., "Moving up to align vertically with the turtle at row 5.").

**3. Final Decision:**
- If the objective is complete (no targets are left), you MUST return 'none' for the direction.
- If no logical or safe move can be made, return 'none'.
- Your response MUST be in the specified JSON format.
`,
});

const suggestMoveDirectionFlow = ai.defineFlow(
  {
    name: 'suggestMoveDirectionFlow',
    inputSchema: SuggestMoveDirectionInputSchema,
    outputSchema: SuggestMoveDirectionOutputSchema,
  },
  async input => {
    const {output} = await prompt(input);
    return output!;
  }
);
