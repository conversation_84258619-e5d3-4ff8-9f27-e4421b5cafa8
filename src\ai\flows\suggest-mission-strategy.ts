'use server';

/**
 * @fileOverview A function that suggests high-level strategy and next few moves for missions.
 *
 * - suggestMissionStrategy - A function that provides strategic guidance for missions
 * - SuggestMissionStrategyInput - The input type for the function
 * - SuggestMissionStrategyOutput - The return type for the function
 */

import { createStructuredCompletion } from '@/ai/openai-client';
import { z } from 'zod';

const LocationSchema = z.object({
  row: z.number(),
  col: z.number(),
});

const SuggestMissionStrategyInputSchema = z.object({
  grid: z.array(z.array(z.string())).describe('A 2D array representing the game grid.'),
  botRow: z.number().describe('The row index of the bot on the grid.'),
  botCol: z.number().describe('The column index of the bot on the grid.'),
  objective: z.string().describe('The mission objective.'),
  turtleLocations: z.array(LocationSchema).describe('The locations of all turtles on the grid.'),
  batteryLocations: z.array(LocationSchema).describe('The locations of all batteries on the grid.'),
});

export type SuggestMissionStrategyInput = z.infer<typeof SuggestMissionStrategyInputSchema>;

const SuggestMissionStrategyOutputSchema = z.object({
  primaryTarget: LocationSchema.describe('The next target to focus on.'),
  strategy: z.string().describe('High-level strategy explanation.'),
  nextAction: z.enum(['move', 'fire']).describe('The immediate action to take.'),
  direction: z.enum(['up', 'down', 'left', 'right']).describe('The direction for the action.'),
  reason: z.string().describe('Detailed reasoning for this decision.'),
  alternativeTargets: z.array(LocationSchema).describe('Alternative targets in order of priority.'),
});

export type SuggestMissionStrategyOutput = z.infer<typeof SuggestMissionStrategyOutputSchema>;

export async function suggestMissionStrategy(input: SuggestMissionStrategyInput): Promise<SuggestMissionStrategyOutput> {
  const gridDisplay = input.grid.map(row => row.join('')).join('\n');
  
  let batteryInfo = 'None';
  if (input.batteryLocations && input.batteryLocations.length > 0) {
    batteryInfo = input.batteryLocations.map((loc, i) => `  ${i+1}. (${loc.row}, ${loc.col}) - distance: ${Math.abs(loc.row - input.botRow) + Math.abs(loc.col - input.botCol)}`).join('\n');
  }
  
  let turtleInfo = 'None';
  if (input.turtleLocations && input.turtleLocations.length > 0) {
    turtleInfo = input.turtleLocations.map((loc, i) => `  ${i+1}. (${loc.row}, ${loc.col}) - distance: ${Math.abs(loc.row - input.botRow) + Math.abs(loc.col - input.botCol)}`).join('\n');
  }

  const prompt = `You are a strategic AI advisor for the Emoji Bot Arena game. Your task is to provide high-level strategy and determine the best immediate action for the current mission.

**COORDINATE SYSTEM:**
- Row 0 is at the TOP, row numbers INCREASE going DOWN
- Column 0 is at the LEFT, column numbers INCREASE going RIGHT
- Moving 'up' DECREASES row number, 'down' INCREASES row number
- Moving 'left' DECREASES column, 'right' INCREASES column

**CURRENT GAME STATE:**
- Bot position: (${input.botRow}, ${input.botCol})
- Mission: "${input.objective}"
- Grid:
${gridDisplay}

- Batteries 🔋 (with distances):
${batteryInfo}

- Turtles 🐢 (with distances):
${turtleInfo}

**STRATEGIC ANALYSIS REQUIRED:**

**For "collect all batteries" missions:**
1. **Target Prioritization**: Choose the optimal next battery considering:
   - Manhattan distance from bot
   - Safety (avoid turtle-heavy areas)
   - Path efficiency (same row/column preferred)
   - Strategic positioning for subsequent targets

2. **Risk Assessment**: Evaluate turtle threats along the path
3. **Route Planning**: Consider the most efficient order to visit remaining batteries

**For "eliminate all turtles" missions:**
1. **Target Prioritization**: Choose the optimal next turtle considering:
   - Line-of-sight availability (same row/column)
   - Distance to reach firing position
   - Multi-target opportunities (can hit multiple turtles)
   - Safety of approach path

2. **Positioning Strategy**: Plan movement to get clear shots
3. **Efficiency**: Minimize movement by choosing well-positioned targets

**DECISION FRAMEWORK:**
1. Analyze all possible targets with their pros/cons
2. Choose the PRIMARY target (best overall choice)
3. Identify ALTERNATIVE targets (backup options)
4. Determine immediate action (move toward target or fire if aligned)
5. Provide clear strategic reasoning

**MOVEMENT LOGIC:**
- If target row > bot row: move 'down'
- If target row < bot row: move 'up'
- If target row = bot row AND target col > bot col: move 'right'
- If target row = bot row AND target col < bot col: move 'left'

**FIRING LOGIC:**
- Only fire if bot and target are on same row OR same column
- Check for obstacles (batteries) blocking the shot

Provide strategic guidance and the next immediate action.

Respond with a JSON object in this exact format:
{
  "primaryTarget": {"row": 10, "col": 5},
  "strategy": "Targeting battery at (10,5) because it's closest and in same column",
  "nextAction": "move",
  "direction": "down",
  "reason": "Moving down toward primary target at (10,5). Distance: 5 steps.",
  "alternativeTargets": [{"row": 3, "col": 8}, {"row": 12, "col": 2}]
}`;

  return await createStructuredCompletion<SuggestMissionStrategyOutput>(
    prompt,
    SuggestMissionStrategyOutputSchema
  );
}
