'use server';

/**
 * @fileOverview A Genkit flow that processes natural language commands for the Emoji Bot Arena game.
 *
 * - processChatCommand - A function that interprets a chat message and returns a game command.
 * - ProcessChatCommandInput - The input type for the processChatCommand function.
 * - ProcessChatCommandOutput - The return type for the processChatCommand function.
 */

import {ai} from '@/ai/genkit';
import {z} from 'zod';
import { suggestLaserDirection, type SuggestLaserDirectionInput } from './suggest-laser-direction';
import { SuggestLaserDirectionInputSchema } from '@/ai/schemas';

const LocationSchema = z.object({
  row: z.number(),
  col: z.number(),
});

const ProcessChatCommandInputSchema = z.object({
  command: z.string().describe('The natural language command from the user.'),
  grid: z
    .array(z.array(z.string()))
    .describe('A 2D array representing the game grid.'),
  botRow: z.number().describe('The row index of the bot on the grid.'),
  botCol: z.number().describe('The column index of the bot on the grid.'),
  turtleLocations: z.array(LocationSchema).describe('The locations of all turtles on the grid.'),
  batteryLocations: z.array(LocationSchema).describe('The locations of all batteries on the grid.'),
});
export type ProcessChatCommandInput = z.infer<
  typeof ProcessChatCommandInputSchema
>;

const ActionSchema = z.object({
  action: z
    .enum(['move', 'fire', 'unknown'])
    .describe("The game action to perform."),
  direction: z
    .enum(['up', 'down', 'left', 'right', 'none'])
    .describe("The direction for the action."),
});

const ProcessChatCommandOutputSchema = z.object({
  actions: z.array(ActionSchema).describe("A sequence of game actions to perform. This should be empty if a high-level mission is identified."),
  response: z.string().describe('A natural language response to the user about the action being taken.'),
  mission: z.enum(['collect_batteries', 'eliminate_turtles', 'none']).optional().describe("The high-level mission identified from the command. If a mission is found, 'actions' should be empty. Set to 'none' for simple, direct commands."),
  toolLog: z.string().nullable().optional().describe('A log of any tool calls made, including the tool name and key arguments.'),
  reasoning: z.string().nullable().optional().describe("The reasoning provided by the tool for its suggestion."),
});
export type ProcessChatCommandOutput = z.infer<
  typeof ProcessChatCommandOutputSchema
>;

export async function processChatCommand(
  input: ProcessChatCommandInput
): Promise<ProcessChatCommandOutput> {
  return processChatCommandFlow(input);
}

const getOptimalFireDirectionTool = ai.defineTool(
    {
        name: 'getOptimalFireDirection',
        description: 'Determines the best direction to fire the laser to hit a turtle, based on the current game state. Use this when the user asks to fire, shoot, or kill something but does not specify a direction.',
        inputSchema: SuggestLaserDirectionInputSchema,
        outputSchema: z.object({
            direction: z.enum(['up', 'down', 'left', 'right']),
            reason: z.string(),
        }),
    },
    async (input: SuggestLaserDirectionInput) => suggestLaserDirection(input)
);

const prompt = ai.definePrompt({
  name: 'processChatCommandPrompt',
  model: 'googleai/gemini-2.0-flash',
  input: {schema: ProcessChatCommandInputSchema},
  output: {schema: ProcessChatCommandOutputSchema},
  tools: [getOptimalFireDirectionTool],
  prompt: `You are the AI controlling the bot in the Emoji Bot Arena game. Your task is to interpret a user's natural language command and translate it into a structured command for the game.

**Primary Task: Classify the Command**

You must first determine if the command is a **Simple Action** or a **High-Level Mission**.

1.  **Simple Actions**:
    *   **Description**: These are direct, explicit instructions that can be translated into one or more basic game actions immediately.
    *   **Examples**: "go left", "shoot up", "move left 3 units", "go up and then shoot right", "go to row 10, column 5".
    *   **Your Output**:
        *   Generate a complete sequence of one or more action objects in the 'actions' array.
        *   Set the \`mission\` field to "none" or omit it entirely.
        *   Provide a suitable \`response\`, e.g., "Okay, moving left three times."
    *   **Tool Use**: For vague fire commands like "destroy the turtle" or "fire laser", you MUST use the \`getOptimalFireDirection\` tool. The tool's output should be used to create a single 'fire' action.

2.  **High-Level Missions**:
    *   **Description**: These are broad, objective-based commands that require multiple steps to complete, like collecting all items of a certain type.
    *   **Examples**: "collect all the batteries", "get every battery", "eliminate all turtles", "clear the board of turtles".
    *   **Your Output**:
        *   You MUST NOT generate the step-by-step actions yourself.
        *   The 'actions' array MUST be empty.
        *   Identify the mission and set the \`mission\` field to either \`"collect_batteries"\` or \`"eliminate_turtles"\`.
        *   Provide a confirmation message in the \`response\` field, e.g., "Roger that. Starting mission: collect all batteries."

3.  **Unclear Commands**:
    *   If you cannot understand the command, set \`actions\` to \`[{ action: 'unknown', direction: 'none' }]\` and provide a clarifying \`response\`.

**Grid Coordinate System:**
- The grid's origin (row: 0, col: 0) is at the **TOP-LEFT** corner.
- Moving **UP** DECREASES the row index.
- Moving **DOWN** INCREASES the row index.
- Moving **LEFT** DECREASES the column index.
- Moving **RIGHT** INCREASES the column index.

**Game Context (Source of Truth):**
- User command: "{{command}}"
- Bot: (row: {{botRow}}, col: {{botCol}})
- Turtles:
{{#if turtleLocations}}
{{#each turtleLocations}}
  - (row: {{this.row}}, col: {{this.col}})
{{/each}}
{{else}}
  None
{{/if}}
- Batteries:
{{#if batteryLocations}}
{{#each batteryLocations}}
  - (row: {{this.row}}, col: {{this.col}})
{{/each}}
{{else}}
  None
{{/if}}
- Grid (for visual context):
{{#each grid}}
{{#each this}}{{{this}}}{{/each}}
{{/each}}

**VERY IMPORTANT JSON FORMATTING RULES:**
Your entire response MUST be a single, valid JSON object. Do not include any other text, explanations, or markdown formatting.
An action object must always look like this: \`{ "action": "move", "direction": "up" }\`.
NEVER swap the values. For example, \`{ "action": "up", "direction": "move" }\` is INVALID and will fail.
The only valid values for 'action' are "move", "fire", and "unknown". Collecting a battery is an automatic result of moving onto its square.
`,
});

const processChatCommandFlow = ai.defineFlow(
  {
    name: 'processChatCommandFlow',
    inputSchema: ProcessChatCommandInputSchema,
    outputSchema: ProcessChatCommandOutputSchema,
  },
  async input => {
    const {output} = await prompt(input);
    return output!;
  }
);
