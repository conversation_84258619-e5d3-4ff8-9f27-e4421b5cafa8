'use server';

/**
 * @fileOverview A function that suggests the optimal direction to move the bot.
 *
 * - suggestMoveDirection - A function that suggests the best direction to move.
 * - SuggestMoveDirectionInput - The input type for the suggestMoveDirection function.
 * - SuggestMoveDirectionOutput - The return type for the suggestMoveDirection function.
 */

import { createStructuredCompletion } from '@/ai/openai-client';
import { z } from 'zod';
import { SuggestMoveDirectionInputSchema } from '@/ai/schemas';

export type SuggestMoveDirectionInput = z.infer<
  typeof SuggestMoveDirectionInputSchema
>;

const SuggestMoveDirectionOutputSchema = z.object({
  direction: z
    .enum(['up', 'down', 'left', 'right', 'none'])
    .describe('The suggested direction to move the bot. "none" if no move is possible or needed.'),
  reason: z.string().describe('The reasoning behind the suggested direction.'),
});
export type SuggestMoveDirectionOutput = z.infer<
  typeof SuggestMoveDirectionOutputSchema
>;

export async function suggestMoveDirection(
  input: SuggestMoveDirectionInput
): Promise<SuggestMoveDirectionOutput> {
  const gridDisplay = input.grid.map(row => row.join('')).join('\n');

  let batteryInfo = '';
  if (input.batteryLocations && input.batteryLocations.length > 0) {
    batteryInfo = `- Batteries 🔋 locations:\n${input.batteryLocations.map(loc => `  - row ${loc.row}, col ${loc.col}`).join('\n')}\n`;
  }

  let turtleInfo = '';
  if (input.turtleLocations && input.turtleLocations.length > 0) {
    turtleInfo = `- Turtles 🐢 locations:\n${input.turtleLocations.map(loc => `  - row ${loc.row}, col ${loc.col}`).join('\n')}\n`;
  }

  const prompt = `You are a meticulous and logical AI assistant playing the Emoji Bot Arena game. Your task is to suggest the single most optimal move for the bot to achieve a given objective. You must be precise and avoid getting stuck in loops.

**CRITICAL COORDINATE SYSTEM UNDERSTANDING:**
- Row 0 is at the TOP of the grid
- Row numbers INCREASE as you go DOWN
- Column 0 is at the LEFT of the grid
- Column numbers INCREASE as you go RIGHT
- Moving 'up' DECREASES row number (towards row 0)
- Moving 'down' INCREASES row number (away from row 0)
- Moving 'left' DECREASES column number (towards column 0)
- Moving 'right' INCREASES column number (away from column 0)

**1. Analyze the Game State:**
- The bot 🤖 is at row ${input.botRow}, column ${input.botCol}.
- The user's high-level objective is: "${input.objective}"
- The grid is:
${gridDisplay}
${batteryInfo}${turtleInfo}

**2. Determine the Best Move based on Objective:**

**If the objective contains "batteries":**
a. **CRITICAL: Calculate Manhattan distance correctly for each battery:**
   - Distance = |target_row - bot_row| + |target_col - bot_col|
   - Choose the battery with the SMALLEST distance
   - If tied, prefer: same column > same row > lower row > lower column

b. **Choose Direction Logic - FOLLOW EXACTLY:**
   - If target row > bot row: move 'down' (increase row number)
   - If target row < bot row: move 'up' (decrease row number)
   - If target row = bot row AND target col > bot col: move 'right' (increase column)
   - If target row = bot row AND target col < bot col: move 'left' (decrease column)
   - If target row = bot row AND target col = bot col: target reached, return 'none'

c. **MANDATORY DISTANCE CALCULATION EXAMPLE:**
   - Bot at (5,10), Battery A at (1,6): distance = |1-5| + |6-10| = 4 + 4 = 8
   - Bot at (5,10), Battery B at (13,10): distance = |13-5| + |10-10| = 8 + 0 = 8
   - SAME DISTANCE: Choose Battery B because same column (easier path)

d. **Safety Check**:
   - Check if your chosen move lands on a turtle ('🐢'). If so, try alternative moves.
   - If no safe direct move exists, try moving perpendicular to go around obstacles.
   - If no safe move is possible, return 'none'.

e. **ALWAYS show your distance calculations in the reason.**

**If the objective contains "turtle":**
a. Find the turtle with the minimum Manhattan distance from the bot. This is your target for this turn.
b. **FIRST CHECK - Can we fire now?**
   - If bot and target are on same row OR same column, check if path is clear (no batteries blocking)
   - If path is clear, return 'none' with reason "Ready to fire at turtle at (row, col)"
   - If path is blocked by battery, proceed to alignment strategy
c. **Alignment Strategy (when not ready to fire):**
   - Priority 1: Get on same row as target (up if target row < bot row, down if target row > bot row)
   - Priority 2: If already on same row, get on same column (left if target col < bot col, right if target col > bot col)
d. **CRITICAL SAFETY CHECKS:**
   - Ensure the move doesn't land on a turtle ('🐢')
   - If aligned but path blocked by battery ('🔋'), move perpendicular to re-approach from different angle
e. Provide clear reasoning with coordinate analysis and fire readiness status.

**3. Final Decision:**
- If the objective is complete (no targets left), return 'none'
- If no safe move exists, return 'none'
- Always double-check your coordinate math before responding

**EXAMPLE COORDINATE LOGIC:**
- Bot at (6,1), Battery at (9,1): target row 9 > bot row 6, so move 'down'
- Bot at (6,1), Battery at (3,1): target row 3 < bot row 6, so move 'up'
- Bot at (6,1), Battery at (6,5): same row, target col 5 > bot col 1, so move 'right'
- Bot at (5,10), Battery at (1,6): target row 1 < bot row 5, so move 'up'
- Bot at (5,10), Battery at (13,10): target row 13 > bot row 5, so move 'down'

**DISTANCE CALCULATION EXAMPLES:**
- Bot (5,10) to Battery (1,6): |1-5| + |6-10| = 4 + 4 = 8
- Bot (5,10) to Battery (13,10): |13-5| + |10-10| = 8 + 0 = 8
- Equal distance: Choose (13,10) because same column = easier path

Respond with a JSON object in this exact format:
{
  "direction": "<direction>",
  "reason": "<reason>"
}

Make sure the direction is one of: up, down, left, right, or none.`;

  return await createStructuredCompletion<SuggestMoveDirectionOutput>(
    prompt,
    SuggestMoveDirectionOutputSchema
  );
}


