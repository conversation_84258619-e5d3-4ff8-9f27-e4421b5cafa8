# AI Migration from Gemini to OpenAI-Compatible LLM

This document explains the changes made to migrate the AI agent from Google's Gemini (via Genkit) to an OpenAI-compatible API (like Codestral).

## Changes Made

### 1. Dependencies Updated
- **Removed**: `@genkit-ai/googleai`, `@genkit-ai/next`, `genkit`, `genkit-cli`
- **Added**: `openai`

### 2. Configuration Changes
- **Old**: `src/ai/genkit.ts` (Genkit configuration)
- **New**: `src/ai/openai-client.ts` (OpenAI client configuration)

### 3. Environment Variables
Updated `.env` file with new variables:
```env
# OpenAI-compatible API configuration
OPENAI_API_KEY=your-codestral-api-key
OPENAI_API_BASE=your-codestral-api-base-url
```

### 4. AI Flow Implementations
All three AI flows have been updated:
- `src/ai/flows/suggest-laser-direction.ts`
- `src/ai/flows/suggest-move-direction.ts` 
- `src/ai/flows/process-chat-command.ts`

**Key Changes**:
- Replaced Genkit's `ai.definePrompt()` and `ai.defineFlow()` with direct OpenAI API calls
- Converted Handlebars templates to JavaScript template literals
- Maintained the same input/output schemas for compatibility
- Added structured completion helper function for JSON response validation

## Setup Instructions

### 1. Configure Environment Variables
Edit the `.env` file and replace the placeholder values:

```env
# Replace these with your actual Codestral API credentials
OPENAI_API_KEY=your-actual-codestral-api-key
OPENAI_API_BASE=https://your-codestral-api-endpoint.com/v1
```

### 2. Update Model Name (if needed)
In `src/ai/openai-client.ts`, update the model name to match your Codestral model:

```typescript
// Change this to match your actual model name
export const defaultModel = 'codestral-latest';
```

### 3. Test the Connection
Run the test script to verify your API configuration:

```bash
node test-ai.js
```

This will test:
- Environment variable configuration
- API connectivity
- JSON response parsing

### 4. Start the Application
```bash
npm run dev
```

## Architecture Overview

### Before (Genkit)
```
User Input → Genkit Flow → Gemini API → Structured Response
```

### After (OpenAI-Compatible)
```
User Input → Direct Function → OpenAI-Compatible API → Structured Response
```

## Key Features Maintained

1. **Same Interface**: All function signatures remain unchanged
2. **Schema Validation**: Zod schemas still validate inputs/outputs
3. **Error Handling**: Robust error handling for API failures
4. **Tool Integration**: The laser direction suggestion tool still works
5. **Game Logic**: All game mechanics remain identical

## Troubleshooting

### Common Issues

1. **API Key Not Working**
   - Verify your API key is correct
   - Check if your API endpoint requires authentication headers

2. **Model Not Found**
   - Update the `defaultModel` in `src/ai/openai-client.ts`
   - Check available models in your Codestral dashboard

3. **JSON Parsing Errors**
   - The system expects JSON responses
   - Ensure your model supports `response_format: { type: 'json_object' }`

4. **Rate Limiting**
   - Add retry logic if needed
   - Check your API rate limits

### Debug Mode
Enable debug logging by adding to your `.env`:
```env
NODE_ENV=development
```

## Performance Considerations

- **Response Time**: OpenAI-compatible APIs may have different latency
- **Rate Limits**: Check your API provider's rate limits
- **Cost**: Monitor API usage costs with your provider

## Migration Benefits

1. **Flexibility**: Can use any OpenAI-compatible API
2. **Cost Control**: Choose your preferred pricing model
3. **Performance**: Potentially better performance with specialized models
4. **Independence**: Not tied to Google's ecosystem

## Next Steps

1. Test all game features thoroughly
2. Monitor API usage and costs
3. Consider implementing caching for repeated requests
4. Add retry logic for improved reliability

## Files Modified

- `package.json` - Updated dependencies and scripts
- `.env` - Added OpenAI configuration
- `src/ai/genkit.ts` → `src/ai/openai-client.ts` - New client configuration
- `src/ai/flows/suggest-laser-direction.ts` - Updated to use OpenAI
- `src/ai/flows/suggest-move-direction.ts` - Updated to use OpenAI
- `src/ai/flows/process-chat-command.ts` - Updated to use OpenAI
- `src/ai/schemas.ts` - Updated comments
- `src/ai/dev.ts` - Marked as deprecated

## Support

If you encounter issues:
1. Check the test script output
2. Verify environment variables
3. Test API connectivity manually
4. Review console logs for detailed error messages
