'use server';

/**
 * @fileOverview Simple mission planning without function calling - compatible with all OpenAI-compatible APIs
 */

import { createStructuredCompletion } from '@/ai/openai-client';
import { z } from 'zod';

export type SimpleMissionInput = {
  grid: string[][];
  botRow: number;
  botCol: number;
  objective: string;
  turtleLocations: Array<{row: number, col: number}>;
  batteryLocations: Array<{row: number, col: number}>;
};

const SimpleMissionOutputSchema = z.object({
  action: z.enum(['move', 'fire']).describe("The action to take"),
  direction: z.enum(['up', 'down', 'left', 'right', 'none']).describe("The direction for the action"),
  reason: z.string().describe("Detailed reasoning for this decision"),
  targetAnalysis: z.string().describe("Analysis of target selection"),
  distanceCalculations: z.string().describe("Distance calculations performed"),
});

export type SimpleMissionOutput = z.infer<typeof SimpleMissionOutputSchema>;

// Helper functions for calculations
function calculateDistance(from: {row: number, col: number}, to: {row: number, col: number}): number {
  return Math.abs(to.row - from.row) + Math.abs(to.col - from.col);
}

function findBestTarget(
  botRow: number, 
  botCol: number, 
  targets: Array<{row: number, col: number}>, 
  obstacles: Array<{row: number, col: number}> = []
) {
  const analyzed = targets.map(target => {
    const distance = calculateDistance({row: botRow, col: botCol}, target);
    
    let score = 20 - distance; // Base score from distance
    
    // Bonus for same row/column (easier path)
    if (target.row === botRow) score += 10; // Same row
    if (target.col === botCol) score += 15; // Same column (even better)
    
    // Penalty for nearby obstacles
    const nearbyObstacles = obstacles.filter(obs => 
      Math.abs(obs.row - target.row) <= 1 && Math.abs(obs.col - target.col) <= 1
    ).length;
    score -= nearbyObstacles * 3;
    
    return { target, distance, score };
  });
  
  analyzed.sort((a, b) => b.score - a.score);
  return analyzed[0];
}

function getDirectionToTarget(botRow: number, botCol: number, targetRow: number, targetCol: number): string {
  if (targetRow > botRow) return 'down';
  if (targetRow < botRow) return 'up';
  if (targetCol > botCol) return 'right';
  if (targetCol < botCol) return 'left';
  return 'none';
}

function canFireAtTarget(botRow: number, botCol: number, targetRow: number, targetCol: number): {canFire: boolean, direction?: string} {
  if (botRow === targetRow) {
    return { canFire: true, direction: targetCol > botCol ? 'right' : 'left' };
  }
  if (botCol === targetCol) {
    return { canFire: true, direction: targetRow > botRow ? 'down' : 'up' };
  }
  return { canFire: false };
}

export async function executeSimpleMission(input: SimpleMissionInput): Promise<SimpleMissionOutput> {
  const { botRow, botCol, objective, batteryLocations, turtleLocations } = input;
  
  // Pre-calculate all the analysis
  let targetAnalysis = '';
  let distanceCalculations = '';
  let bestTarget: {row: number, col: number} | null = null;
  let action: 'move' | 'fire' = 'move';
  let direction: string = 'none';
  let reasoning = '';

  if (objective.includes('batteries') && batteryLocations.length > 0) {
    // Battery collection mission
    const distances = batteryLocations.map(battery => ({
      battery,
      distance: calculateDistance({row: botRow, col: botCol}, battery)
    }));
    
    distanceCalculations = distances.map(d => 
      `Battery at (${d.battery.row},${d.battery.col}): distance ${d.distance}`
    ).join(', ');
    
    const best = findBestTarget(botRow, botCol, batteryLocations, turtleLocations);
    bestTarget = best.target;
    
    targetAnalysis = `Selected battery at (${best.target.row},${best.target.col}) with score ${best.score} (distance: ${best.distance})`;
    
    direction = getDirectionToTarget(botRow, botCol, best.target.row, best.target.col);
    action = 'move';
    reasoning = `Moving ${direction} toward optimal battery target. Target chosen based on distance and path safety.`;
    
  } else if (objective.includes('turtle') && turtleLocations.length > 0) {
    // Turtle elimination mission
    const distances = turtleLocations.map(turtle => ({
      turtle,
      distance: calculateDistance({row: botRow, col: botCol}, turtle)
    }));
    
    distanceCalculations = distances.map(d => 
      `Turtle at (${d.turtle.row},${d.turtle.col}): distance ${d.distance}`
    ).join(', ');
    
    // Check if we can fire at any turtle
    let canFireAtAny = false;
    for (const turtle of turtleLocations) {
      const fireCheck = canFireAtTarget(botRow, botCol, turtle.row, turtle.col);
      if (fireCheck.canFire) {
        bestTarget = turtle;
        action = 'fire';
        direction = fireCheck.direction!;
        reasoning = `Firing ${direction} at turtle at (${turtle.row},${turtle.col}) - clear line of sight`;
        targetAnalysis = `Can fire at turtle at (${turtle.row},${turtle.col}) from current position`;
        canFireAtAny = true;
        break;
      }
    }
    
    if (!canFireAtAny) {
      // Need to move to get line of sight
      const best = findBestTarget(botRow, botCol, turtleLocations, []);
      bestTarget = best.target;
      direction = getDirectionToTarget(botRow, botCol, best.target.row, best.target.col);
      action = 'move';
      reasoning = `Moving ${direction} to align with turtle at (${best.target.row},${best.target.col}) for clear shot`;
      targetAnalysis = `Selected turtle at (${best.target.row},${best.target.col}) for positioning`;
    }
  } else {
    // No targets available
    direction = 'none';
    reasoning = 'Mission complete - no more targets available';
    targetAnalysis = 'No valid targets found';
    distanceCalculations = 'No calculations needed';
  }

  // Now ask the LLM to validate and potentially improve the decision
  const gridDisplay = input.grid.map(row => row.join('')).join('\n');
  
  const prompt = `You are an expert AI strategist. I've pre-calculated the optimal move for the current mission, but I want you to validate it and provide the final decision.

GAME STATE:
- Bot position: (${botRow}, ${botCol})
- Mission: ${objective}
- Grid:
${gridDisplay}

PRE-CALCULATED ANALYSIS:
- Target Analysis: ${targetAnalysis}
- Distance Calculations: ${distanceCalculations}
- Recommended Action: ${action} ${direction}
- Reasoning: ${reasoning}

COORDINATE SYSTEM:
- Row 0 is TOP, numbers INCREASE going DOWN
- Column 0 is LEFT, numbers INCREASE going RIGHT
- Moving 'up' DECREASES row, 'down' INCREASES row

Please validate this decision or suggest improvements. Consider:
1. Is the target selection optimal?
2. Is the direction calculation correct?
3. Are there any safety concerns?
4. Could the strategy be improved?

Provide your final decision in the required JSON format.`;

  return await createStructuredCompletion<SimpleMissionOutput>(
    prompt,
    SimpleMissionOutputSchema
  );
}
